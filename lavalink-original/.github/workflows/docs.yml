name: Docs Push

on:
  push:
    branches: [ '**' ]
    paths:
      - 'docs/**'
      - '.github/workflows/docs.yml'

concurrency:
  group: pages-${{ github.ref }}
  cancel-in-progress: true

jobs:
  deploy:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      deployments: write
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - uses: actions/setup-python@v5
        with:
          python-version: 3.x
      - run: echo "cache_id=$(date --utc '+%V')" >> $GITHUB_ENV
      - uses: actions/cache@v4
        with:
          key: mkdocs-material-${{ env.cache_id }}
          path: .cache
          restore-keys: |
            mkdocs-material-
      - run: pip install -r requirements.txt
        working-directory: docs
      #      - run: mkdocs build --verbose --strict
      - run: mkdocs build --verbose
        working-directory: docs
      - uses: actions/upload-pages-artifact@v3
        with:
          path: 'site'
      - uses: cloudflare/pages-action@v1
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          projectName: ${{ vars.CLOUDFLARE_PROJECT_NAME }}
          directory: site
          gitHubToken: ${{ secrets.GITHUB_TOKEN }}
          wranglerVersion: '3'
