name: Release

on:
  release:
    types: [ published ]

jobs:
  build:
    uses: ./.github/workflows/build.yml
    secrets:
      DOCKER_USERNAME: ${{ secrets.DOCKER_USERNAME }}
      DOCKER_TOKEN: ${{ secrets.DOCKER_TOKEN }}
      DOCKER_REGISTRY: ${{ secrets.DOCKER_REGISTRY }}
      DOCKER_IMAGE: ${{ secrets.DOCKER_IMAGE }}
      MAVEN_USERNAME: ${{ secrets.MAVEN_USERNAME }}
      MAVEN_PASSWORD: ${{ secrets.MAVEN_PASSWORD }}
      MAVEN_CENTRAL_USERNAME: ${{ secrets.MAVEN_CENTRAL_USERNAME }}
      MAVEN_CENTRAL_PASSWORD: ${{ secrets.MAVEN_CENTRAL_PASSWORD }}
      SIGNING_IN_MEMORY_KEY: ${{ secrets.SIGNING_IN_MEMORY_KEY }}
      SIGNING_IN_MEMORY_KEY_PASSWORD: ${{ secrets.SIGNING_IN_MEMORY_KEY_PASSWORD }}

  release:
    needs: build
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Download Lavalink.jar
        uses: actions/download-artifact@v4
        with:
          name: Lavalink.jar

      - name: Download Lavalink-musl.jar
        uses: actions/download-artifact@v4
        with:
          name: Lavalink-musl.jar

      - name: Upload Artifacts to GitHub Release
        uses: ncipollo/release-action@v1
        with:
          artifacts: Lavalink.jar,Lavalink-musl.jar
          allowUpdates: true
          omitBodyDuringUpdate: true
          omitDraftDuringUpdate: true
          omitNameDuringUpdate: true
          omitPrereleaseDuringUpdate: true
