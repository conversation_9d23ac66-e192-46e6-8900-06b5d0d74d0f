.nt-cards.nt-grid {
    display: grid;
    grid-auto-columns: 1fr;
    gap: 0.5rem;
    max-width: 100vw;
    overflow-x: auto;
    padding: 1rem;
}

.nt-cards.nt-grid.cols-2 {
    grid-template-columns: repeat(2, 1fr);
}

.nt-cards.nt-grid.cols-3 {
    grid-template-columns: repeat(3, 1fr);
}

@media only screen and (max-width: 900px) {
    .nt-cards.nt-grid {
        grid-template-columns: repeat(2, 1fr) !important;
    }
}

@media only screen and (max-width: 600px) {
    .nt-cards.nt-grid {
        grid-template-columns: repeat(1, 1fr) !important;
    }
}

.nt-card {
    border-radius: 4px;
    box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 1px 5px 0 rgba(0, 0, 0, 0.12);
    background-color: var(--md-typeset-kbd-color);
}

.nt-card:hover {
    box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.24), 0 3px 1px -2px rgba(0, 0, 0, 0.3), 0 1px 5px 0 rgba(0, 0, 0, 0.22);
}

.nt-card-wrap > div,
.nt-card > a > div {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    padding: .625em;
    border-radius: 4px;
}

.nt-card > a > div:hover {
    background-color: var(--md-typeset-kbd-color);
}

.nt-card-content {
    width: 100%;
}

.nt-card-title {
    font-weight: bold;
    margin: 4px 0 8px 0;
    text-align: center;
    color: var(--md-default-fg-color);
}

.nt-card-icon {
    color: var(--md-default-fg-color);
}

.nt-card-icon svg,
.nt-card-icon .twemoji,
.nt-card-icon .icon {
    display: block;
    width: 34px !important;
    height: 34px !important;
    max-height: 34px !important;
}
