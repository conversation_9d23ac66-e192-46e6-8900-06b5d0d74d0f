---
description: <PERSON><PERSON><PERSON> frequently asked questions.
---

# FAQ

## What is Laval<PERSON>?

Lavalink is a standalone audio player node that is used to stream music to Discord voice servers. It is written in Java and is based on [lavaplayer](https://github.com/lavalink-devs/lavaplayer) and [koe](https://github.com/KyokoBot/koe).

## What is Lavalink used for?

Lavalink is used to stream music to Discord voice servers. It is used by many Discord music bots, including [<PERSON><PERSON><PERSON>](https://fredboat.com) and many others.

## How do I install Lavalink?

See the [Getting Started](index.md) for instructions on how to install Lavalink.

## How do I configure Lavalink?

See the [Configuration](../configuration/index.md) page for instructions on how to configure Lavalink.

## How do I connect to Lavalink?

See the [Clients](../clients.md) page for a list of clients that can connect to Lavalink. Each client has its own instructions on how to connect to Lavalink.

## How do I run Lavalink in the background?

See the [Docker](../configuration/docker.md) or [Systemd](../configuration/systemd.md) configuration pages for instructions on how to run Lavalink in the background.

## How do I update Lavalink?

Updating Lavalink is as simple as downloading the latest `Lavalink.jar` from [GitHub](https://github.com/lavalink-devs/Lavalink/releases/latest) and replacing the old jar file with the new one.
When using Docker, you can simply pull the latest image from [GitHub Container Registry](https://github.com/lavalink-devs/Lavalink/pkgs/container/lavalink).

## How do I report a bug?

Open an issue on the [issue tracker](https://github.com/lavalink-devs/Lavalink/issues/new?labels=bug&template=bug_report.md).

## How do I get help?

Join the [Lavalink support Discord]({{ discord_help }}) or open a [GitHub discussion](https://github.com/lavalink-devs/Lavalink/discussions/new?category=q-a).

## How do I get support for a client?

Open an issue on the client's GitHub repository or join the client's support Discord. The [Lavalink support Discord]({{ discord }}) also has a channel for each client where you can get support.