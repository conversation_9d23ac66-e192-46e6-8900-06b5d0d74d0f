---
description: Lavalink getting started guide.
---

# Getting Started

Welcome to the Lavalink Getting Started guide. If you're new to Lavalink, follow these steps to get started:

## Prerequisites

Before you start, make sure you have picked an uptodate Lavalink client. You can find a list of clients on the [clients](../clients.md) page.

## Running Lavalink

Generally there are a few different ways to run Lavalink:

### Standalone Binary

> **Note:** This is the easiest way to run Lavalink.

To run Lavalink as a standalone binary, follow the steps [here](binary.md).

### Systemd

> **Note:** This is the recommended way to run Lavalink on Linux.

Running Lavalink as a systemd service makes sure that Lavalink is always running and restarts it if it crashes.
You can find the instructions [here](systemd.md).

### Docker

> **Note:** This is the recommended way for experienced users to run Lavalink.

Running applications in Docker containers is a great way to isolate them from the host system and make them portable.
You can find the instructions [here](docker.md).

## Getting stuck?

Check out the [Troubleshooting section](../troubleshooting.md) and the [FAQ](faq.md) for common issues and questions.

If you are still stuck, you can join the [Lavalink support Discord]({{ discord_help }}) or open a [GitHub discussion](https://github.com/lavalink-devs/Lavalink/discussions/new?category=q-a) for help or questions.
