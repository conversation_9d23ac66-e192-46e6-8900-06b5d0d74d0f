---
description: How to configure Lavalink
---

# Configuration

There are mainly 3 different ways to configure Lavalink:

## Config File

This is the easiest way to configure Lavalink. You create a file called `application.yml` where you start your Lavalink server.
See the [application.yml](config/application-yml.md) documentation for more information.

## Config Server

> **WARNING:** This is an advanced feature geared towards big deployments.

You can run the [Lavalink Config Server](https://github.com/lavalink-devs/Lavalink-Config-Server) to manage the configuration of multiple Lavalink servers at one place.
See the [Config Server](config/config-server.md) documentation for more information.

## Environment Variables

You can also configure Lavalink using environment variables.
See the [environment variables](config/environment-variables.md) documentation for more information.

<details markdown="1">
<summary>environment variables</summary>

```env title="enviroment variables"
SERVER_PORT
SERVER_ADDRESS
SERVER_HTTP2_ENABLED

LAVALINK_PLUGINS_0_DEPENDENCY
LAVALINK_PLUGINS_0_REPOSITORY
LAVALINK_PLUGINS_0_SNAPSHOT

LAVALINK_PLUGINS_1_DEPENDENCY
LAVALINK_PLUGINS_1_REPOSITORY
LAVALINK_PLUGINS_1_SNAPSHOT

LAVALINK_PLUGINS_DIR
LAVALINK_DEFAULT_PLUGIN_REPOSITORY
LAVALINK_DEFAULT_PLUGIN_SNAPSHOT_REPOSITORY

LAVALINK_SERVER_PASSWORD
LAVALINK_SERVER_SOURCES_YOUTUBE
LAVALINK_SERVER_SOURCES_BANDCAMP
LAVALINK_SERVER_SOURCES_SOUNDCLOUD
LAVALINK_SERVER_SOURCES_TWITCH
LAVALINK_SERVER_SOURCES_VIMEO
LAVALINK_SERVER_SOURCES_NICO
LAVALINK_SERVER_SOURCES_HTTP
LAVALINK_SERVER_SOURCES_LOCAL

LAVALINK_SERVER_FILTERS_VOLUME
LAVALINK_SERVER_FILTERS_EQUALIZER
LAVALINK_SERVER_FILTERS_KARAOKE
LAVALINK_SERVER_FILTERS_TIMESCALE
LAVALINK_SERVER_FILTERS_TREMOLO
LAVALINK_SERVER_FILTERS_VIBRATO
LAVALINK_SERVER_FILTERS_DISTORTION
LAVALINK_SERVER_FILTERS_ROTATION
LAVALINK_SERVER_FILTERS_CHANNEL_MIX
LAVALINK_SERVER_FILTERS_LOW_PASS

LAVALINK_SERVER_NON_ALLOCATING_FRAME_BUFFER
LAVALINK_SERVER_BUFFER_DURATION_MS
LAVALINK_SERVER_FRAME_BUFFER_DURATION_MS
LAVALINK_SERVER_OPUS_ENCODING_QUALITY
LAVALINK_SERVER_RESAMPLING_QUALITY
LAVALINK_SERVER_TRACK_STUCK_THRESHOLD_MS
LAVALINK_SERVER_USE_SEEK_GHOSTING

LAVALINK_SERVER_PLAYER_UPDATE_INTERVAL
LAVALINK_SERVER_YOUTUBE_PLAYLIST_LOAD_LIMIT
LAVALINK_SERVER_YOUTUBE_SEARCH_ENABLED
LAVALINK_SERVER_SOUNDCLOUD_SEARCH_ENABLED

LAVALINK_SERVER_GC_WARNINGS

LAVALINK_SERVER_RATELIMIT_IP_BLOCKS
LAVALINK_SERVER_RATELIMIT_EXCLUDE_IPS
LAVALINK_SERVER_RATELIMIT_STRATEGY
LAVALINK_SERVER_RATELIMIT_SEARCH_TRIGGERS_FAIK
LAVALINK_SERVER_RATELIMIT_RETRY_LIMIT

LAVALINK_SERVER_YOUTUBE_CONFIG_EMAIL
LAVALINK_SERVER_YOUTUBE_CONFIG_PASSWORD

LAVALINK_SERVER_HTTP_CONFIG_PROXY_HOST
LAVALINK_SERVER_HTTP_CONFIG_PROXY_PORT
LAVALINK_SERVER_HTTP_CONFIG_PROXY_USER
LAVALINK_SERVER_HTTP_CONFIG_PROXY_PASSWORD

METRICS_PROMETHEUS_ENABLED
METRICS_PROMETHEUS_ENDPOINT

SENTRY_DSN
SENTRY_ENVIRONMENT
SENTRY_TAGS_SOME_KEY
SENTRY_TAGS_ANOTHER_KEY

LOGGING_FILE_PATH
LOGGING_LEVEL_ROOT
LOGGING_LEVEL_LAVALINK

LOGGING_REQUEST_ENABLED
LOGGING_REQUEST_INCLUDE_CLIENT_INFO
LOGGING_REQUEST_INCLUDE_HEADERS
LOGGING_REQUEST_INCLUDE_QUERY_STRING
LOGGING_REQUEST_INCLUDE_PAYLOAD
LOGGING_REQUEST_MAX_PAYLOAD_LENGTH

LOGGING_LOGBACK_ROLLINGPOLICY_MAX_FILE_SIZE
LOGGING_LOGBACK_ROLLINGPOLICY_MAX_HISTORY
```

</details>

You can also use a combination of both. Environment variables take precedence over the `application.yml` file.
