## v2.2

* Lavaplayer updated to 1.3.x [\#115](https://github.com/lavalink-devs/Lavalink/pull/115)
* Version command line flag [\#121](https://github.com/lavalink-devs/Lavalink/pull/121)
* Fix race condition in `/loadtracks` endpoint leading to some requests never completing [\#125](https://github.com/lavalink-devs/Lavalink/pull/125)

Contributors:
[@Devoxin](https://github.com/Devoxin),
[@freyacodes](https://github.com/freyacodes/),
[@napstr](https://github.com/napstr)

## v2.1

* Add prometheus metrics [\#105](https://github.com/lavalink-devs/Lavalink/pull/105), [\#106](https://github.com/lavalink-devs/Lavalink/pull/106)

Contributors:
[@freyacodes](https://github.com/freyacodes/),
[@napstr](https://github.com/napstr),
[@Repulser](https://github.com/Repulser/)

## v2.0.1

* Configurable playlist load limit [\#60](https://github.com/lavalink-devs/Lavalink/pull/60)
* [Docker Releases](https://hub.docker.com/r/fredboat/lavalink/), [\#74](https://github.com/lavalink-devs/Lavalink/pull/74)

Contributors:
[@Devoxin](https://github.com/Devoxin),
[@freyacodes](https://github.com/freyacodes/),
[@itslukej](https://github.com/itslukej/),
[@napstr](https://github.com/napstr),
[@Repulser](https://github.com/Repulser/)

## v2.0

Please see [here](https://github.com/lavalink-devs/Lavalink/commit/b8dd3c8a7e186755c1ab343d19a552baecf138e7)
and [here](https://github.com/lavalink-devs/Lavalink/commit/08a34c99a47a18ade7bd14e6c55ab92348caaa88)
