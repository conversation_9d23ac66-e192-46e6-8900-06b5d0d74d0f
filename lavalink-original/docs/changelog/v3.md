## v3.7.13
!!! danger

    This is the last Lavalink `v3` release, see [here](https://github.com/lavalink-devs/Lavalink/discussions/1100) for more info.

* Updated Lavaplayer to [`1.5.6`](https://github.com/lavalink-devs/lavaplayer/releases/tag/1.5.6)
* Updated koe to [`2.0.3-rc2`](https://github.com/KyokoBot/koe/releases/tag/2.0.3-rc2) & use voice gateway v8 in [#1098](https://github.com/lavalink-devs/Lavalink/pull/1098)

## v3.7.12
* Updated Lavaplayer to [`1.5.4`](https://github.com/lavalink-devs/lavaplayer/releases/tag/1.5.4)
* Updated Koe to [`2.0.1`](https://github.com/KyokoBot/koe/releases/tag/2.0.1) (fixes the `IndexOutOfBoundsException` when playing a YouTube track)

!!! warning

    The default Youtube source is now deprecated and won't receive further updates. Please use https://github.com/lavalink-devs/youtube-source#plugin instead.

## v3.7.11
* Fixed not being able to seek when player is paused 
* Updated Oshi to `6.4.3`
* Updated Lavaplayer to `1.5.3`

## v3.7.10
* Updated lavaplayer to [`1.5.2`](https://github.com/lavalink-devs/lavaplayer/releases/tag/1.5.2) - Fixed NPE on missing author in playlist tracks in YouTube

## v3.7.9
* Updated lavaplayer to [`1.5.1`](https://github.com/lavalink-devs/lavaplayer/releases/tag/1.5.1) - Fixed YouTube access token errors
* Fixed websocket crash when seeking and nothing is playing
* Fixed error when seeking and player is not playing anything

## v3.7.8

* Fix YouTube 403 errors
* Fix YouTube access token errors

## v3.7.7

* Add JDA-NAS support for musl (`x86-64`, `aarch64`) based systems (most notably `alpine`)

## v3.7.6

* Update Lavaplayer to [`1.4.1`](https://github.com/Walkyst/lavaplayer-fork/releases/tag/1.4.1) & [`1.4.2`](https://github.com/Walkyst/lavaplayer-fork/releases/tag/1.4.2)
* New support for `MUSL` based systems (most notably `alpine`)
* New `alpine` docker image variant (use `-alpine` suffix)

## v3.7.5

* Fix `endTime` in `Player Update` endpoint only applying when playing a new track
* Fix errors when doing multiple session resumes
* Update lavaplayer to `1.4.0` see [here](https://github.com/Walkyst/lavaplayer-fork/releases/tag/1.4.0) for more info

!!! info

    Lavalink Docker images are now found in the GitHub Container Registry instead of DockerHub

## v3.7.4

* Fix an issue where Lavalink would not destroy a session when a client disconnects

## v3.7.3

* Fix breaking change where `/decodetrack` would return a full track instead of the track info

## v3.7.2

* Fix breaking change where frameStats would be null instead of omitted

## v3.7.1

* Revert of application.yml autocreate as it can cause issues with differently named configs

## v3.7.0

* New REST API for player control and deprecation of all websocket OPs. For more info see [here](https://github.com/lavalink-devs/Lavalink/blob/master/IMPLEMENTATION.md#significant-changes-v360---v370)
* Autocreate default `application.yml` if none was found. https://github.com/lavalink-devs/Lavalink/pull/781
* New config option to disable jda nas. https://github.com/lavalink-devs/Lavalink/pull/780
* New config option to disable specific filters. https://github.com/lavalink-devs/Lavalink/pull/779
* Update lavaplayer to `1.3.99.2`. https://github.com/lavalink-devs/Lavalink/pull/794
* Update udpqueue.rs to `v0.2.6`. https://github.com/lavalink-devs/Lavalink/pull/802

Contributors:
[@topi314](https://github.com/topi314), [@Devoxin](https://github.com/Devoxin), [@melike2d](https://github.com/melike2d), [@freyacodes](https://github.com/freyacodes), [@aikaterna](https://github.com/aikaterna), [@ooliver1](https://github.com/ooliver1)

## v3.6.2

* Update lavaplayer to `********`. For more info see [here](https://github.com/lavalink-devs/Lavalink/pull/773)

## v3.6.1

* Update lavaplayer to `1.3.99`. For more info see [here](https://github.com/lavalink-devs/Lavalink/pull/768)

## v3.6.0

* New userId & clientName getters in the plugin-api. For more info see [here](https://github.com/lavalink-devs/Lavalink/pull/743).

Contributors:
[@melike2d](https://github.com/melike2d)

## v3.5.1

* Update udpqueue.rs to `0.2.5` which fixes crashes when ipv6 is disabled
* Fix null socketContext in `IPlayer` for plugins
* New `ping` field in player update. see https://github.com/lavalink-devs/Lavalink/pull/738 for more info

Contributors:
[@topi314](https://github.com/topi314), [@Devoxin](https://github.com/Devoxin), and [@freyacodes](https://github.com/freyacodes)

## v3.5

* New plugin system. For more info see [here](https://github.com/lavalink-devs/Lavalink/blob/master/PLUGINS.md).
* Add support for HTTP proxying via httpConfig. For more info see [here](https://github.com/lavalink-devs/Lavalink/pull/595).
* Update koe version to 2.0.0-rc1.
    - this fixes the WebSocketClosedEvent with code 1006 problem.
* Fix error when enabling timescale and lowpass filters.
* Fix player not playing after moving between voice chats or changing regions.
* Fix guild ids sent as numbers in json.
* Fix missing timescale natives.
* Fix setting endMarkerHit to correctly set FINISHED as the reason.
* Undeprecation of the `volume` property in the `play` OP.
* Configurable track stuck threshold. For more info see [here](https://github.com/lavalink-devs/Lavalink/pull/676).
* Add JDA-NAS support for more CPU Architectures. For more info see [here](https://github.com/lavalink-devs/Lavalink/pull/692). Big thanks goes to @MinnDevelopment here.
* Update lavaplayer to [`********`](https://github.com/Walkyst/lavaplayer-fork/releases/tag/********) which fixes the latest yt cipher issues and age restricted tracks

Contributors:
[@freyacodes](https://github.com/freyacodes),
[@davidffa](https://github.com/davidffa),
[@Walkyst](https://github.com/Walkyst),
[@topi314](https://github.com/topi314),
[@duncte123](https://github.com/duncte123),
[@Kodehawa](https://github.com/Kodehawa),
[@Devoxin](https://github.com/Devoxin),
[@Muh9049](https://github.com/Muh9049),
[@melike2d](https://github.com/melike2d),
[@ToxicMushroom](https://github.com/ToxicMushroom),
[@mooner1022](https://github.com/mooner1022),
[@rohank05](https://github.com/rohank05),
[@Fabricio20](https://github.com/Fabricio20),
[@TheEssemm](https://github.com/TheEssemm), and
[@jack1142](https://github.com/jack1142)

## v3.4

* New filters system
* Deprecation of `TrackExceptionEvent.error`, replaced by `TrackExceptionEvent.exception`
* Added the `connected` boolean to player updates.
* Updated lavaplayer, fixes Soundcloud
* Added source name to REST api track objects
* Clients are now requested to make their name known during handshake

Contributors:
[@freyacodes](https://github.com/freyacodes),
[@duncte123](https://github.com/duncte123),
[@DaliborTrampota](https://github.com/DaliborTrampota),
[@Mandruyd](https://github.com/Mandruyd),
[@Allvaa](https://github.com/@Allvaa), and
[@topi314](https://github.com/topi314)

## v3.3.2.5

* Update Lavaplayer to 1.3.76

## v3.3.2.4

* Update Lavaplayer to 1.3.74

## v3.3.2.3

* Update Lavaplayer to 1.3.65, fixes Soundcloud

## v3.3.2.2

* Updated Lavaplayer to 1.3.61
* Fixed a ConcurrentModificationException ([Thewsomeguy](https://github.com/Thewsomeguy))

## v3.3.2.1

* Updated to Sedmelluq's Lavaplayer 1.3.53

## v3.3.2

* Replaced Magma with Koe.
* Finally implemented `stopTime` for `play` op.
* Added `playerUpdateInterval` config option.
* Added `environment` to Sentry config.
* Fixed #332
* Updated IP rotator.
* Update lavaplayer to `1.3.59` from devoxin's fork.
* Added a Testbot for development.

Contributors:
[@freyacodes](https://github.com/freyacodes),
[@Thewsomeguy](https://github.com/Thewsomeguy),
[@Neuheit](https://github.com/Neuheit),
[@Sangoon_Is_Noob](https://github.com/Sangoon_Is_Noob),
[@TheEssem](https://github.com/Essem), and
[@Devoxin](https://github.com/Devoxin)

## v3.3.1.4

* Update lavaplayer to `********` from devoxin's fork.

## v3.3.1.3

* Update lavaplayer to `1.3.53` from devoxin's fork.

## v3.3.1.2

* Update lavaplayer to [@Devoxin](https://github.com/Devoxin)'s' fork

## v3.3.1.1

* Updated Lavaplayer to `1.3.50`. This notably fixes YouTube search.

Search patch contributed by [@freyacodes](https://github.com/freyacodes)

## v3.3.1

* Update Magma and Lavaplayer.
* Added TrackStartEvent event.
* Added retryLimit configuration option.
* Use a single AudioPlayerManager for all WS connections, reducing overhead.
* Docker images now use Zulu JDK 13 to mitigate TLS 1.3 problems.

Contributors:
[@freyacodes](https://github.com/freyacodes),
[@duncte123](https://github.com/duncte123),
[@ByteAlex](https://github.com/ByteAlex), and
[@Xavinlol](https://github.com/Xavinlol)

## v3.3

Officially limit Lavalink to JRE 11 and up. Magma has long been having issues with older versions.

## v3.2.2

* IP rotation system for getting around certain ratelimits.
* Update Lavaplayer to 1.3.32.
* Docker container now uses a non-root user.

Contributors:
[@freyacodes](https://github.com/freyacodes),
[@ByteAlex](https://github.com/ByteAlex),
[@duncte123](https://github.com/duncte123), and
[@james7132](https://github.com/james7132)

## v3.2.1.1

* Updated Lavaplayer to 1.3.19. This release includes a patch which fixes loading youtube URLs.
  https://github.com/sedmelluq/lavaplayer/pull/199
* Made the WebSocket handshake return code 401 instead of 200 on bad auth. #208

Contributors:
[@freyacodes](https://github.com/freyacodes) and
[@Devoxin](https://github.com/Devoxin)

## v3.2.1

* Update dependencies -- fixes frequent youtube HTTP errors
* Return `FriendlyException` message on `LOAD_FAILED` #174
* Add option to disable `ytsearch` and `scsearch` #194

Contributors:
[@Devoxin](https://github.com/Devoxin),
[@duncte123](https://github.com/duncte123),
[@freyacodes](https://github.com/freyacodes), and
[@napstr](https://github.com/napstr)

## v3.2.0.3

* Add compatibility for Java 8-10

Contributor:
[@MinnDevelopment](https://github.com/MinnDevelopment/)

## v3.2.0.2

* Patched magma

Contributor:
[@freyacodes](https://github.com/freyacodes/)

## v3.2.0.1

* Bumped to Java 11. Treating this as a patch version, as v3.2 still requires Java 11 due to a Magma update.

[@freyacodes](https://github.com/freyacodes)

## v3.2

* Added support for resuming
* Added noReplace option to the play op
* Sending the same voice server update will not cause an existing connection to reconnect

Contributor:
[@freyacodes](https://github.com/freyacodes)

## v3.1.2

* Add API version header to all responses

Contributor:
[@Devoxin](https://github.com/Devoxin)

## v3.1.1

* Add equalizer support
* Update lavaplayer to 1.3.10
* Fixed automatic versioning
* Added build config to upload binaries to GitHub releases from CI

Contributors:
[@Devoxin](https://github.com/Devoxin),
[@freyacodes](https://github.com/freyacodes/),
[@calebj](https://github.com/calebj)

## v3.1

* Replaced JDAA with Magma
* Added an event for when the Discord voice WebSocket is closed
* Replaced Tomcat and Java_Websocket with Undertow. WS and REST is now handled by the same
  server and port. Port is specified by `server.port`.

## v3.0

* **Breaking:** The minimum required Java version to run the server is now Java 10.   
  **Please note**: Java 10 will be obsolete
  as of [September 2018 with the release of Java 11](http://www.java-countdown.xyz/). Expect a Lavalink major version release that will be targetting
  Java 11 by that time.
* **Breaking:** Changes to the output of the /loadtracks endpoint. [\#91](https://github.com/lavalink-devs/Lavalink/pull/91), [\#114](https://github.com/lavalink-devs/Lavalink/pull/114), [\#116](https://github.com/lavalink-devs/Lavalink/pull/116)
* **Breaking:** The Java client has been moved to a [new repository](https://github.com/lavalink-devs/Lavalink-Client).
* **Breaking:** The Java client has been made generic. This is a breaking change so please read the [migration guide](https://github.com/lavalink-devs/Lavalink-Client#migrating-from-v2-to-v3).
* Better configurable logging. [\#97](https://github.com/lavalink-devs/Lavalink/pull/97)
* Add custom sentry tags, change sentry dsn configuration location. [\#103](https://github.com/lavalink-devs/Lavalink/pull/103)
* Add Lavalink version header to websocket handshake. [\#111](https://github.com/lavalink-devs/Lavalink/pull/111)
* Use git tags for easier version visibility. [\#129](https://github.com/lavalink-devs/Lavalink/pull/129)

Contributors:
[@Devoxin](https://github.com/Devoxin),
[@freyacodes](https://github.com/freyacodes/),
[@napstr](https://github.com/napstr),
[@SamOphis](https://github.com/SamOphis)
