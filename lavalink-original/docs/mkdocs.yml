# yaml-language-server: $schema=https://squidfunk.github.io/mkdocs-material/schema.json

site_name: Lavalink Docs
site_description: Lavalink Documentation
site_author: Lavalink Contributors
site_url: https://lavalink.dev
site_dir: ../site
docs_dir: .

repo_name: Lavalink
repo_url: https://github.com/lavalink-devs/Lavalink
edit_uri: edit/master/docs/

copyright: Licensed under the MIT license

nav:
  - Home: index.md
  - Getting Started:
      - getting-started/index.md
      - Binary: getting-started/binary.md
      - Systemd: getting-started/systemd.md
      - Docker: getting-started/docker.md
      - Troubleshooting: getting-started/troubleshooting.md
      - FAQ: getting-started/faq.md
  - Configuration:
      - configuration/index.md
      - Config:
          - File: configuration/config/file.md
          - Environment Variables: configuration/config/environment-variables.md
          - Server: configuration/config/server.md
      - RoutePlanner: configuration/routeplanner.md
      - IPv6:
          - configuration/ipv6/index.md
          - Tunnelbroker: configuration/ipv6/tunnelbroker.md
          - Ubuntu/Debian: configuration/ipv6/ubuntudebian.md
          - Hetzner: configuration/ipv6/hetzner.md
          - Contabo: configuration/ipv6/contabo.md
          - DigitalOcean: configuration/ipv6/digitalocean.md
  - Clients: clients.md
  - Plugins: plugins.md
  - API:
      - api/index.md
      - Websocket: api/websocket.md
      - Rest: api/rest.md
      - Plugins: api/plugins.md
  - Changelog:
      - changelog/index.md
      - v4: changelog/v4.md
      - v3: changelog/v3.md
      - v2: changelog/v2.md

extra_css:
  - stylesheets/style.css
  - stylesheets/neoteroi-cards.css

extra:
  homepage: /
  discord: https://discord.gg/BTHvsc7WsT
  discord_help: https://discord.gg/ZW4s47Ppw4
  social:
    - icon: fontawesome/brands/github
      link: https://github.com/lavalink-devs
    - icon: fontawesome/brands/discord
      link: https://discord.gg/BTHvsc7WsT

theme:
  name: material
  custom_dir: overrides
  palette:
    - media: "(prefers-color-scheme: light)"
      scheme: default
      primary: custom
      accent: custom
      toggle:
        icon: material/brightness-7
        name: Switch to dark mode

    - media: "(prefers-color-scheme: dark)"
      scheme: slate
      primary: custom
      accent: custom
      toggle:
        icon: material/brightness-4
        name: Switch to light mode
  features:
    - navigation.instant
    - navigation.tracking
    - navigation.tabs
    - navigation.top
    - navigation.tabs.sticky
    - navigation.footer
    - navigation.path
    - navigation.indexes
    - toc.follow
    - content.code.annotate
    - announce.dismiss
  font:
    text: Roboto
    code: Roboto Mono
  favicon: assets/favicon.png
  logo: assets/logo.svg
  icon:
    repo: fontawesome/brands/github

markdown_extensions:
  - admonition
  - meta
  - pymdownx.details
  - pymdownx.superfences
  - pymdownx.highlight
  - pymdownx.caret
  - pymdownx.mark
  - pymdownx.tilde
  - footnotes
  - def_list
  - attr_list
  - md_in_html
  - toc:
      permalink: true
  - pymdownx.tasklist:
      custom_checkbox: true
  - pymdownx.tabbed:
      alternate_style: true
  - pymdownx.emoji:
      emoji_index: !!python/name:material.extensions.emoji.twemoji
      emoji_generator: !!python/name:material.extensions.emoji.to_svg
  - pymdownx.snippets:
      check_paths: true
      base_path: ../
  - neoteroi.cards

plugins:
  - offline
  - search:
      lang: en
  - same-dir
  - social:
      cards_layout_options:
        background_color: "#ff624a"
        color: "#FFFFFF"
  - git-revision-date-localized
  - markdownextradata
  - redirects:
      redirect_maps:
        discord/index.html: "https://discord.gg/BTHvsc7WsT"
