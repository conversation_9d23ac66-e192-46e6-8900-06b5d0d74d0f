//! Comprehensive logging utilities for voice connection events
//! 
//! This module provides structured logging, correlation tracking, performance metrics,
//! and enhanced error context for the voice connection system.

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::atomic::{AtomicU64, Ordering};
use std::time::{Duration, Instant, SystemTime, UNIX_EPOCH};
use tracing::{debug, error, info, trace, warn};
use uuid::Uuid;

/// Global correlation ID counter for generating unique IDs
static CORRELATION_COUNTER: AtomicU64 = AtomicU64::new(1);

/// Voice event correlation ID for tracking related events
#[derive(Debug, <PERSON>lone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct CorrelationId(String);

impl CorrelationId {
    /// Generate a new correlation ID
    pub fn new() -> Self {
        let counter = CORRELATION_COUNTER.fetch_add(1, Ordering::SeqCst);
        let uuid = Uuid::new_v4();
        Self(format!("voice-{}-{}", counter, uuid.simple()))
    }

    /// Create correlation ID from string
    pub fn from_string(id: String) -> Self {
        Self(id)
    }

    /// Get the correlation ID as a string
    pub fn as_str(&self) -> &str {
        &self.0
    }
}

impl Default for CorrelationId {
    fn default() -> Self {
        Self::new()
    }
}

impl std::fmt::Display for CorrelationId {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}

/// Performance timing utility for measuring operation durations
#[derive(Debug)]
pub struct PerformanceTimer {
    operation: String,
    start_time: Instant,
    correlation_id: CorrelationId,
    guild_id: Option<String>,
}

impl PerformanceTimer {
    /// Start timing an operation
    pub fn start(operation: &str, correlation_id: CorrelationId, guild_id: Option<String>) -> Self {
        trace!(
            correlation_id = %correlation_id,
            guild_id = guild_id.as_deref().unwrap_or("unknown"),
            operation = operation,
            "Starting performance timer"
        );
        
        Self {
            operation: operation.to_string(),
            start_time: Instant::now(),
            correlation_id,
            guild_id,
        }
    }

    /// Complete timing and log the duration
    pub fn complete(self) -> Duration {
        let duration = self.start_time.elapsed();
        
        info!(
            correlation_id = %self.correlation_id,
            guild_id = self.guild_id.as_deref().unwrap_or("unknown"),
            operation = %self.operation,
            duration_ms = duration.as_millis(),
            "Operation completed"
        );
        
        duration
    }

    /// Complete timing with custom log level and additional context
    pub fn complete_with_context(self, success: bool, context: HashMap<String, String>) -> Duration {
        let duration = self.start_time.elapsed();
        
        let log_level = if success { "info" } else { "warn" };
        
        match log_level {
            "info" => info!(
                correlation_id = %self.correlation_id,
                guild_id = self.guild_id.as_deref().unwrap_or("unknown"),
                operation = %self.operation,
                duration_ms = duration.as_millis(),
                success = success,
                context = ?context,
                "Operation completed with context"
            ),
            "warn" => warn!(
                correlation_id = %self.correlation_id,
                guild_id = self.guild_id.as_deref().unwrap_or("unknown"),
                operation = %self.operation,
                duration_ms = duration.as_millis(),
                success = success,
                context = ?context,
                "Operation completed with warnings"
            ),
            _ => {}
        }
        
        duration
    }
}

/// Voice event types for structured logging
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum VoiceEventType {
    ConnectionStart,
    ConnectionEstablished,
    ConnectionFailed,
    ConnectionClosed,
    AudioStreamStart,
    AudioStreamStop,
    AudioQualityChange,
    RecoveryAttempt,
    CircuitBreakerOpen,
    CircuitBreakerClosed,
    HealthCheck,
    AlertGenerated,
    PoolOperation,
    StateTransition,
}

/// Structured voice event for consistent logging
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VoiceEvent {
    pub correlation_id: CorrelationId,
    pub event_type: VoiceEventType,
    pub guild_id: String,
    pub timestamp: u64,
    pub details: HashMap<String, String>,
    pub metrics: Option<HashMap<String, f64>>,
}

impl VoiceEvent {
    /// Create a new voice event
    pub fn new(
        correlation_id: CorrelationId,
        event_type: VoiceEventType,
        guild_id: String,
    ) -> Self {
        Self {
            correlation_id,
            event_type,
            guild_id,
            timestamp: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
            details: HashMap::new(),
            metrics: None,
        }
    }

    /// Add detail to the event
    pub fn with_detail(mut self, key: &str, value: &str) -> Self {
        self.details.insert(key.to_string(), value.to_string());
        self
    }

    /// Add multiple details to the event
    pub fn with_details(mut self, details: HashMap<String, String>) -> Self {
        self.details.extend(details);
        self
    }

    /// Add metrics to the event
    pub fn with_metrics(mut self, metrics: HashMap<String, f64>) -> Self {
        self.metrics = Some(metrics);
        self
    }

    /// Log the event at the appropriate level
    pub fn log(self) {
        match self.event_type {
            VoiceEventType::ConnectionFailed | VoiceEventType::CircuitBreakerOpen => {
                error!(
                    correlation_id = %self.correlation_id,
                    guild_id = %self.guild_id,
                    event_type = ?self.event_type,
                    timestamp = self.timestamp,
                    details = ?self.details,
                    metrics = ?self.metrics,
                    "Voice event occurred"
                );
            }
            VoiceEventType::RecoveryAttempt | VoiceEventType::AudioQualityChange => {
                warn!(
                    correlation_id = %self.correlation_id,
                    guild_id = %self.guild_id,
                    event_type = ?self.event_type,
                    timestamp = self.timestamp,
                    details = ?self.details,
                    metrics = ?self.metrics,
                    "Voice event occurred"
                );
            }
            _ => {
                info!(
                    correlation_id = %self.correlation_id,
                    guild_id = %self.guild_id,
                    event_type = ?self.event_type,
                    timestamp = self.timestamp,
                    details = ?self.details,
                    metrics = ?self.metrics,
                    "Voice event occurred"
                );
            }
        }
    }
}

/// Error context builder for enhanced error logging
#[derive(Debug, Clone)]
pub struct VoiceErrorContext {
    pub correlation_id: CorrelationId,
    pub guild_id: String,
    pub operation: String,
    pub error_type: String,
    pub context: HashMap<String, String>,
    pub troubleshooting_hints: Vec<String>,
}

impl VoiceErrorContext {
    /// Create a new error context
    pub fn new(
        correlation_id: CorrelationId,
        guild_id: String,
        operation: String,
        error_type: String,
    ) -> Self {
        Self {
            correlation_id,
            guild_id,
            operation,
            error_type,
            context: HashMap::new(),
            troubleshooting_hints: Vec::new(),
        }
    }

    /// Add context information
    pub fn with_context(mut self, key: &str, value: &str) -> Self {
        self.context.insert(key.to_string(), value.to_string());
        self
    }

    /// Add troubleshooting hint
    pub fn with_hint(mut self, hint: &str) -> Self {
        self.troubleshooting_hints.push(hint.to_string());
        self
    }

    /// Log the error with full context
    pub fn log_error(self, error: &anyhow::Error) {
        error!(
            correlation_id = %self.correlation_id,
            guild_id = %self.guild_id,
            operation = %self.operation,
            error_type = %self.error_type,
            error = %error,
            context = ?self.context,
            troubleshooting_hints = ?self.troubleshooting_hints,
            "Voice operation failed with context"
        );
    }
}

/// Macro for logging voice connection events
#[macro_export]
macro_rules! log_voice_connection {
    ($level:ident, $correlation_id:expr, $guild_id:expr, $message:expr) => {
        tracing::$level!(
            correlation_id = %$correlation_id,
            guild_id = %$guild_id,
            component = "voice_connection",
            $message
        );
    };
    ($level:ident, $correlation_id:expr, $guild_id:expr, $message:expr, $($key:ident = $value:expr),+) => {
        tracing::$level!(
            correlation_id = %$correlation_id,
            guild_id = %$guild_id,
            component = "voice_connection",
            $($key = $value),+,
            $message
        );
    };
}

/// Macro for logging voice audio events
#[macro_export]
macro_rules! log_voice_audio {
    ($level:ident, $correlation_id:expr, $guild_id:expr, $message:expr) => {
        tracing::$level!(
            correlation_id = %$correlation_id,
            guild_id = %$guild_id,
            component = "voice_audio",
            $message
        );
    };
    ($level:ident, $correlation_id:expr, $guild_id:expr, $message:expr, $($key:ident = $value:expr),+) => {
        tracing::$level!(
            correlation_id = %$correlation_id,
            guild_id = %$guild_id,
            component = "voice_audio",
            $($key = $value),+,
            $message
        );
    };
}

/// Macro for logging voice pool events
#[macro_export]
macro_rules! log_voice_pool {
    ($level:ident, $correlation_id:expr, $message:expr) => {
        tracing::$level!(
            correlation_id = %$correlation_id,
            component = "voice_pool",
            $message
        );
    };
    ($level:ident, $correlation_id:expr, $message:expr, $($key:ident = $value:expr),+) => {
        tracing::$level!(
            correlation_id = %$correlation_id,
            component = "voice_pool",
            $($key = $value),+,
            $message
        );
    };
}
