// Voice connection monitoring and health checks
// Provides comprehensive monitoring for Discord voice connections

use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant, SystemTime, UNIX_EPOCH};
use tokio::sync::RwLock;
use tokio::time::interval;
use tracing::{debug, info, warn, error};

use crate::voice::connection::{VoiceConnectionEvent, RecoveryStatistics};
use crate::voice::pool::ConnectionMetrics;

/// Voice connection health status
#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, PartialEq, Eq, Serialize, Deserialize)]
pub enum HealthStatus {
    /// Connection is healthy and functioning normally
    Healthy,
    /// Connection has minor issues but is still functional
    Degraded,
    /// Connection has significant issues affecting performance
    Unhealthy,
    /// Connection is completely non-functional
    Critical,
    /// Connection status is unknown or being checked
    Unknown,
}

impl Default for HealthStatus {
    fn default() -> Self {
        HealthStatus::Unknown
    }
}

/// Connection performance metrics
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize)]
pub struct ConnectionPerformanceMetrics {
    /// Average latency in milliseconds
    pub avg_latency_ms: f64,
    /// Packet loss percentage (0.0-100.0)
    pub packet_loss_percent: f64,
    /// Jitter in milliseconds
    pub jitter_ms: f64,
    /// Connection uptime in seconds
    pub uptime_seconds: u64,
    /// Number of reconnections
    pub reconnection_count: u32,
    /// Last successful ping timestamp (seconds since UNIX epoch)
    pub last_ping: Option<u64>,
    /// Audio quality score (0-100)
    pub audio_quality_score: u8,
}

/// Voice connection health check result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HealthCheckResult {
    /// Guild ID being monitored
    pub guild_id: String,
    /// Overall health status
    pub status: HealthStatus,
    /// Performance metrics
    pub metrics: ConnectionPerformanceMetrics,
    /// Last check timestamp (seconds since UNIX epoch)
    pub last_check: u64,
    /// Issues detected during health check
    pub issues: Vec<String>,
    /// Recommendations for improving connection health
    pub recommendations: Vec<String>,
}

/// Monitoring configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MonitoringConfig {
    /// Health check interval in seconds
    pub health_check_interval: u64,
    /// Connection timeout for health checks in seconds
    pub health_check_timeout: u64,
    /// Latency threshold for degraded status (ms)
    pub latency_degraded_threshold: f64,
    /// Latency threshold for unhealthy status (ms)
    pub latency_unhealthy_threshold: f64,
    /// Packet loss threshold for degraded status (%)
    pub packet_loss_degraded_threshold: f64,
    /// Packet loss threshold for unhealthy status (%)
    pub packet_loss_unhealthy_threshold: f64,
    /// Maximum allowed reconnections per hour
    pub max_reconnections_per_hour: u32,
    /// Enable detailed performance tracking
    pub enable_performance_tracking: bool,
    /// Enable automatic remediation
    pub enable_auto_remediation: bool,
}

impl Default for MonitoringConfig {
    fn default() -> Self {
        Self {
            health_check_interval: 30,
            health_check_timeout: 10,
            latency_degraded_threshold: 100.0,
            latency_unhealthy_threshold: 250.0,
            packet_loss_degraded_threshold: 1.0,
            packet_loss_unhealthy_threshold: 5.0,
            max_reconnections_per_hour: 10,
            enable_performance_tracking: true,
            enable_auto_remediation: false,
        }
    }
}

/// Alert severity levels
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum AlertSeverity {
    Info,
    Warning,
    Error,
    Critical,
}

/// Monitoring alert
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MonitoringAlert {
    /// Alert ID
    pub id: String,
    /// Guild ID affected
    pub guild_id: String,
    /// Alert severity
    pub severity: AlertSeverity,
    /// Alert title
    pub title: String,
    /// Alert description
    pub description: String,
    /// Timestamp when alert was created (seconds since UNIX epoch)
    pub created_at: u64,
    /// Whether the alert has been acknowledged
    pub acknowledged: bool,
    /// Suggested actions to resolve the issue
    pub suggested_actions: Vec<String>,
}

/// Voice connection monitor
pub struct VoiceConnectionMonitor {
    /// Monitoring configuration
    config: MonitoringConfig,
    /// Health check results per guild
    health_results: Arc<RwLock<HashMap<String, HealthCheckResult>>>,
    /// Active monitoring alerts
    alerts: Arc<RwLock<HashMap<String, MonitoringAlert>>>,
    /// Performance metrics history
    metrics_history: Arc<RwLock<HashMap<String, Vec<ConnectionPerformanceMetrics>>>>,
    /// Alert callback for external notification systems
    alert_callback: Option<Arc<dyn Fn(MonitoringAlert) + Send + Sync>>,
    /// Health check task handle
    health_check_handle: Option<tokio::task::JoinHandle<()>>,
}

/// Helper function to get current timestamp as seconds since UNIX epoch
fn current_timestamp() -> u64 {
    SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap_or_default()
        .as_secs()
}

impl VoiceConnectionMonitor {
    /// Create a new voice connection monitor
    pub fn new() -> Self {
        Self {
            config: MonitoringConfig::default(),
            health_results: Arc::new(RwLock::new(HashMap::new())),
            alerts: Arc::new(RwLock::new(HashMap::new())),
            metrics_history: Arc::new(RwLock::new(HashMap::new())),
            alert_callback: None,
            health_check_handle: None,
        }
    }

    /// Create monitor with custom configuration
    pub fn with_config(config: MonitoringConfig) -> Self {
        Self {
            config,
            health_results: Arc::new(RwLock::new(HashMap::new())),
            alerts: Arc::new(RwLock::new(HashMap::new())),
            metrics_history: Arc::new(RwLock::new(HashMap::new())),
            alert_callback: None,
            health_check_handle: None,
        }
    }

    /// Set alert callback for external notifications
    pub fn set_alert_callback<F>(&mut self, callback: F)
    where
        F: Fn(MonitoringAlert) + Send + Sync + 'static,
    {
        self.alert_callback = Some(Arc::new(callback));
    }

    /// Start monitoring for a guild
    pub async fn start_monitoring(&mut self, guild_id: String) -> Result<()> {
        info!("Starting voice connection monitoring for guild {}", guild_id);

        // Initialize health check result
        let initial_result = HealthCheckResult {
            guild_id: guild_id.clone(),
            status: HealthStatus::Unknown,
            metrics: ConnectionPerformanceMetrics::default(),
            last_check: current_timestamp(),
            issues: Vec::new(),
            recommendations: Vec::new(),
        };

        {
            let mut health_results = self.health_results.write().await;
            health_results.insert(guild_id.clone(), initial_result);
        }

        // Initialize metrics history
        {
            let mut metrics_history = self.metrics_history.write().await;
            metrics_history.insert(guild_id, Vec::new());
        }

        Ok(())
    }

    /// Stop monitoring for a guild
    pub async fn stop_monitoring(&mut self, guild_id: &str) -> Result<()> {
        info!("Stopping voice connection monitoring for guild {}", guild_id);

        // Remove health results
        {
            let mut health_results = self.health_results.write().await;
            health_results.remove(guild_id);
        }

        // Remove metrics history
        {
            let mut metrics_history = self.metrics_history.write().await;
            metrics_history.remove(guild_id);
        }

        // Remove related alerts
        {
            let mut alerts = self.alerts.write().await;
            alerts.retain(|_, alert| alert.guild_id != guild_id);
        }

        Ok(())
    }

    /// Start the health check background task
    pub async fn start_health_checks(&mut self) {
        if self.health_check_handle.is_some() {
            warn!("Health check task is already running");
            return;
        }

        let config = self.config.clone();
        let health_results = Arc::clone(&self.health_results);
        let alerts = Arc::clone(&self.alerts);
        let metrics_history = Arc::clone(&self.metrics_history);
        let alert_callback = self.alert_callback.clone();

        let handle = tokio::spawn(async move {
            let mut interval = interval(Duration::from_secs(config.health_check_interval));
            
            loop {
                interval.tick().await;
                
                // Get list of guilds to monitor
                let guild_ids: Vec<String> = {
                    let health_results = health_results.read().await;
                    health_results.keys().cloned().collect()
                };

                // Perform health checks for each guild
                for guild_id in guild_ids {
                    if let Err(e) = Self::perform_health_check(
                        &guild_id,
                        &config,
                        &health_results,
                        &alerts,
                        &metrics_history,
                        &alert_callback,
                    ).await {
                        error!("Health check failed for guild {}: {}", guild_id, e);
                    }
                }
            }
        });

        self.health_check_handle = Some(handle);
        info!("Started voice connection health check task");
    }

    /// Stop the health check background task
    pub async fn stop_health_checks(&mut self) {
        if let Some(handle) = self.health_check_handle.take() {
            handle.abort();
            info!("Stopped voice connection health check task");
        }
    }

    /// Perform health check for a specific guild
    async fn perform_health_check(
        guild_id: &str,
        config: &MonitoringConfig,
        health_results: &Arc<RwLock<HashMap<String, HealthCheckResult>>>,
        alerts: &Arc<RwLock<HashMap<String, MonitoringAlert>>>,
        metrics_history: &Arc<RwLock<HashMap<String, Vec<ConnectionPerformanceMetrics>>>>,
        alert_callback: &Option<Arc<dyn Fn(MonitoringAlert) + Send + Sync>>,
    ) -> Result<()> {
        debug!("Performing health check for guild {}", guild_id);

        // Simulate health check (in real implementation, this would check actual connection)
        let mut metrics = ConnectionPerformanceMetrics::default();
        let mut issues = Vec::new();
        let mut recommendations = Vec::new();

        // Simulate latency measurement (in real implementation, measure actual latency)
        metrics.avg_latency_ms = 50.0 + (std::time::SystemTime::now().duration_since(std::time::UNIX_EPOCH).unwrap().as_millis() % 100) as f64;

        // Simulate packet loss (in real implementation, track actual packet loss)
        metrics.packet_loss_percent = (std::time::SystemTime::now().duration_since(std::time::UNIX_EPOCH).unwrap().as_millis() % 20) as f64 / 10.0;

        // Simulate jitter (in real implementation, measure actual jitter)
        metrics.jitter_ms = 5.0 + (std::time::SystemTime::now().duration_since(std::time::UNIX_EPOCH).unwrap().as_millis() % 15) as f64;

        // Update uptime
        metrics.uptime_seconds = 3600; // Placeholder

        // Calculate audio quality score based on metrics
        metrics.audio_quality_score = Self::calculate_audio_quality_score(&metrics);

        // Determine health status
        let status = Self::determine_health_status(&metrics, config);

        // Check for issues and generate recommendations
        if metrics.avg_latency_ms > config.latency_degraded_threshold {
            issues.push(format!("High latency: {:.1}ms", metrics.avg_latency_ms));
            recommendations.push("Consider checking network connectivity".to_string());
        }

        if metrics.packet_loss_percent > config.packet_loss_degraded_threshold {
            issues.push(format!("Packet loss detected: {:.1}%", metrics.packet_loss_percent));
            recommendations.push("Check network stability and bandwidth".to_string());
        }

        // Create health check result
        let result = HealthCheckResult {
            guild_id: guild_id.to_string(),
            status,
            metrics: metrics.clone(),
            last_check: current_timestamp(),
            issues,
            recommendations,
        };

        // Update health results
        {
            let mut health_results = health_results.write().await;
            health_results.insert(guild_id.to_string(), result.clone());
        }

        // Store metrics history
        if config.enable_performance_tracking {
            let mut metrics_history = metrics_history.write().await;
            if let Some(history) = metrics_history.get_mut(guild_id) {
                history.push(metrics);
                // Keep only last 100 entries
                if history.len() > 100 {
                    history.remove(0);
                }
            }
        }

        // Generate alerts if needed
        if status == HealthStatus::Unhealthy || status == HealthStatus::Critical {
            Self::generate_alert(
                guild_id,
                &result,
                alerts,
                alert_callback,
            ).await?;
        }

        Ok(())
    }

    /// Calculate audio quality score based on performance metrics
    fn calculate_audio_quality_score(metrics: &ConnectionPerformanceMetrics) -> u8 {
        let mut score = 100u8;

        // Reduce score based on latency
        if metrics.avg_latency_ms > 50.0 {
            score = score.saturating_sub(((metrics.avg_latency_ms - 50.0) / 10.0) as u8);
        }

        // Reduce score based on packet loss
        if metrics.packet_loss_percent > 0.0 {
            score = score.saturating_sub((metrics.packet_loss_percent * 10.0) as u8);
        }

        // Reduce score based on jitter
        if metrics.jitter_ms > 10.0 {
            score = score.saturating_sub(((metrics.jitter_ms - 10.0) / 2.0) as u8);
        }

        score.max(0)
    }

    /// Determine health status based on metrics and configuration
    fn determine_health_status(
        metrics: &ConnectionPerformanceMetrics,
        config: &MonitoringConfig,
    ) -> HealthStatus {
        // Check for critical conditions
        if metrics.avg_latency_ms > config.latency_unhealthy_threshold * 2.0 ||
           metrics.packet_loss_percent > config.packet_loss_unhealthy_threshold * 2.0 {
            return HealthStatus::Critical;
        }

        // Check for unhealthy conditions
        if metrics.avg_latency_ms > config.latency_unhealthy_threshold ||
           metrics.packet_loss_percent > config.packet_loss_unhealthy_threshold {
            return HealthStatus::Unhealthy;
        }

        // Check for degraded conditions
        if metrics.avg_latency_ms > config.latency_degraded_threshold ||
           metrics.packet_loss_percent > config.packet_loss_degraded_threshold {
            return HealthStatus::Degraded;
        }

        HealthStatus::Healthy
    }

    /// Generate monitoring alert
    async fn generate_alert(
        guild_id: &str,
        result: &HealthCheckResult,
        alerts: &Arc<RwLock<HashMap<String, MonitoringAlert>>>,
        alert_callback: &Option<Arc<dyn Fn(MonitoringAlert) + Send + Sync>>,
    ) -> Result<()> {
        let alert_id = format!("{}_{}", guild_id, current_timestamp());

        let (severity, title) = match result.status {
            HealthStatus::Critical => (AlertSeverity::Critical, "Voice Connection Critical"),
            HealthStatus::Unhealthy => (AlertSeverity::Error, "Voice Connection Unhealthy"),
            HealthStatus::Degraded => (AlertSeverity::Warning, "Voice Connection Degraded"),
            _ => return Ok(()), // No alert needed for healthy connections
        };

        let description = format!(
            "Voice connection for guild {} is {}. Latency: {:.1}ms, Packet Loss: {:.1}%",
            guild_id,
            format!("{:?}", result.status).to_lowercase(),
            result.metrics.avg_latency_ms,
            result.metrics.packet_loss_percent
        );

        let alert = MonitoringAlert {
            id: alert_id.clone(),
            guild_id: guild_id.to_string(),
            severity,
            title: title.to_string(),
            description,
            created_at: current_timestamp(),
            acknowledged: false,
            suggested_actions: result.recommendations.clone(),
        };

        // Store alert
        {
            let mut alerts = alerts.write().await;
            alerts.insert(alert_id, alert.clone());
        }

        // Trigger callback if available
        if let Some(callback) = alert_callback {
            callback(alert);
        }

        Ok(())
    }

    /// Get health status for a specific guild
    pub async fn get_health_status(&self, guild_id: &str) -> Option<HealthCheckResult> {
        let health_results = self.health_results.read().await;
        health_results.get(guild_id).cloned()
    }

    /// Get health status for all monitored guilds
    pub async fn get_all_health_status(&self) -> HashMap<String, HealthCheckResult> {
        let health_results = self.health_results.read().await;
        health_results.clone()
    }

    /// Get active alerts
    pub async fn get_active_alerts(&self) -> Vec<MonitoringAlert> {
        let alerts = self.alerts.read().await;
        alerts.values().filter(|alert| !alert.acknowledged).cloned().collect()
    }

    /// Get all alerts (including acknowledged ones)
    pub async fn get_all_alerts(&self) -> Vec<MonitoringAlert> {
        let alerts = self.alerts.read().await;
        alerts.values().cloned().collect()
    }

    /// Acknowledge an alert
    pub async fn acknowledge_alert(&self, alert_id: &str) -> Result<()> {
        let mut alerts = self.alerts.write().await;
        if let Some(alert) = alerts.get_mut(alert_id) {
            alert.acknowledged = true;
            info!("Alert {} acknowledged", alert_id);
            Ok(())
        } else {
            Err(anyhow::anyhow!("Alert {} not found", alert_id))
        }
    }

    /// Clear acknowledged alerts older than specified duration
    pub async fn clear_old_alerts(&self, max_age: Duration) -> usize {
        let mut alerts = self.alerts.write().await;
        let cutoff_time = current_timestamp() - max_age.as_secs();
        let initial_count = alerts.len();

        alerts.retain(|_, alert| {
            !alert.acknowledged || alert.created_at > cutoff_time
        });

        let cleared_count = initial_count - alerts.len();
        if cleared_count > 0 {
            info!("Cleared {} old acknowledged alerts", cleared_count);
        }
        cleared_count
    }

    /// Get performance metrics history for a guild
    pub async fn get_metrics_history(&self, guild_id: &str) -> Option<Vec<ConnectionPerformanceMetrics>> {
        let metrics_history = self.metrics_history.read().await;
        metrics_history.get(guild_id).cloned()
    }

    /// Get monitoring summary statistics
    pub async fn get_monitoring_summary(&self) -> MonitoringSummary {
        let health_results = self.health_results.read().await;
        let alerts = self.alerts.read().await;

        let mut summary = MonitoringSummary::default();
        summary.total_monitored_guilds = health_results.len() as u32;

        // Count by health status
        for result in health_results.values() {
            match result.status {
                HealthStatus::Healthy => summary.healthy_connections += 1,
                HealthStatus::Degraded => summary.degraded_connections += 1,
                HealthStatus::Unhealthy => summary.unhealthy_connections += 1,
                HealthStatus::Critical => summary.critical_connections += 1,
                HealthStatus::Unknown => summary.unknown_connections += 1,
            }

            // Calculate average metrics
            summary.avg_latency_ms += result.metrics.avg_latency_ms;
            summary.avg_packet_loss_percent += result.metrics.packet_loss_percent;
        }

        if !health_results.is_empty() {
            summary.avg_latency_ms /= health_results.len() as f64;
            summary.avg_packet_loss_percent /= health_results.len() as f64;
        }

        // Count alerts
        summary.total_alerts = alerts.len() as u32;
        summary.unacknowledged_alerts = alerts.values()
            .filter(|alert| !alert.acknowledged)
            .count() as u32;

        summary
    }

    /// Update monitoring configuration
    pub fn update_config(&mut self, config: MonitoringConfig) {
        self.config = config;
        info!("Updated monitoring configuration");
    }

    /// Handle voice connection event for monitoring
    pub async fn handle_voice_event(&self, guild_id: &str, event: &VoiceConnectionEvent) {
        debug!("Handling voice event for guild {}: {:?}", guild_id, event);

        // Update metrics based on event
        let mut health_results = self.health_results.write().await;
        if let Some(result) = health_results.get_mut(guild_id) {
            match event {
                VoiceConnectionEvent::Connected => {
                    result.metrics.reconnection_count += 1;
                    result.status = HealthStatus::Healthy;
                }
                VoiceConnectionEvent::Disconnected => {
                    result.status = HealthStatus::Critical;
                    result.issues.push("Connection lost".to_string());
                }
                VoiceConnectionEvent::Error(error) => {
                    result.status = HealthStatus::Unhealthy;
                    result.issues.push(format!("Connection error: {}", error));
                }
                VoiceConnectionEvent::GatewayReady { .. } => {
                    result.metrics.last_ping = Some(current_timestamp());
                }
                _ => {} // Handle other events as needed
            }
            result.last_check = current_timestamp();
        }
    }
}

/// Monitoring summary statistics
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct MonitoringSummary {
    /// Total number of monitored guilds
    pub total_monitored_guilds: u32,
    /// Number of healthy connections
    pub healthy_connections: u32,
    /// Number of degraded connections
    pub degraded_connections: u32,
    /// Number of unhealthy connections
    pub unhealthy_connections: u32,
    /// Number of critical connections
    pub critical_connections: u32,
    /// Number of unknown status connections
    pub unknown_connections: u32,
    /// Average latency across all connections
    pub avg_latency_ms: f64,
    /// Average packet loss across all connections
    pub avg_packet_loss_percent: f64,
    /// Total number of alerts
    pub total_alerts: u32,
    /// Number of unacknowledged alerts
    pub unacknowledged_alerts: u32,
}

impl Default for VoiceConnectionMonitor {
    fn default() -> Self {
        Self::new()
    }
}
