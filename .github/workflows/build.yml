name: Build

on:
  push:
    branches: [ '**' ]
    paths-ignore:
      - '**.md'
      - 'docs/**'
      - '.github/workflows/docs.yml'
      - '.github/workflows/docs-pr.yml'
  workflow_call:
    secrets:
      DOCKER_REGISTRY:
        required: false
      DOCKER_USERNAME:
        required: false
      DOCKER_TOKEN:
        required: false
      DOCKER_IMAGE:
        required: false
      CRATES_IO_TOKEN:
        required: false

env:
  CARGO_TERM_COLOR: always
  RUST_BACKTRACE: 1

jobs:
  test:
    name: Test Suite
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Install Rust toolchain
        uses: dtolnay/rust-toolchain@stable
        with:
          components: rustfmt, clippy

      - name: Install system dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y \
            pkg-config \
            libssl-dev \
            libopus-dev \
            libavcodec-dev \
            libavformat-dev \
            libavutil-dev \
            libavfilter-dev \
            libavdevice-dev

      - name: Cache Rust dependencies
        uses: Swatinem/rust-cache@v2
        with:
          cache-on-failure: true

      - name: Check formatting
        run: cargo fmt --all -- --check

      - name: Run Clippy
        run: cargo clippy --all-targets --all-features -- -D warnings

      - name: Run tests
        run: cargo test --all-features --workspace

      - name: Run integration tests
        run: cargo test --test integration_tests

  build:
    name: Build
    runs-on: ubuntu-latest
    needs: test
    strategy:
      matrix:
        target:
          - x86_64-unknown-linux-gnu
          - x86_64-unknown-linux-musl
          - aarch64-unknown-linux-gnu
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Install Rust toolchain
        uses: dtolnay/rust-toolchain@stable
        with:
          targets: ${{ matrix.target }}

      - name: Install system dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y \
            pkg-config \
            libssl-dev \
            libopus-dev \
            libavcodec-dev \
            libavformat-dev \
            libavutil-dev \
            libavfilter-dev \
            libavdevice-dev

      - name: Install cross-compilation tools
        if: matrix.target == 'aarch64-unknown-linux-gnu'
        run: |
          sudo apt-get install -y gcc-aarch64-linux-gnu
          echo "CARGO_TARGET_AARCH64_UNKNOWN_LINUX_GNU_LINKER=aarch64-linux-gnu-gcc" >> $GITHUB_ENV

      - name: Install musl tools
        if: matrix.target == 'x86_64-unknown-linux-musl'
        run: |
          sudo apt-get install -y musl-tools
          sudo ln -s /usr/bin/musl-gcc /usr/local/bin/x86_64-linux-musl-gcc

      - name: Cache Rust dependencies
        uses: Swatinem/rust-cache@v2
        with:
          key: ${{ matrix.target }}
          cache-on-failure: true

      - name: Build binary
        env:
          # Use vendored OpenSSL for cross-compilation targets to avoid linking issues
          OPENSSL_STATIC: ${{ (matrix.target == 'x86_64-unknown-linux-musl' || matrix.target == 'aarch64-unknown-linux-gnu') && '1' || '' }}
          OPENSSL_VENDORED: ${{ (matrix.target == 'x86_64-unknown-linux-musl' || matrix.target == 'aarch64-unknown-linux-gnu') && '1' || '' }}
        run: cargo build --release --target ${{ matrix.target }}

      - name: Create artifact name
        id: artifact
        run: |
          case ${{ matrix.target }} in
            x86_64-unknown-linux-gnu)
              echo "name=lavalink-rust-linux-x64" >> $GITHUB_OUTPUT
              echo "binary=lavalink-rust" >> $GITHUB_OUTPUT
              ;;
            x86_64-unknown-linux-musl)
              echo "name=lavalink-rust-linux-x64-musl" >> $GITHUB_OUTPUT
              echo "binary=lavalink-rust-musl" >> $GITHUB_OUTPUT
              ;;
            aarch64-unknown-linux-gnu)
              echo "name=lavalink-rust-linux-arm64" >> $GITHUB_OUTPUT
              echo "binary=lavalink-rust-arm64" >> $GITHUB_OUTPUT
              ;;
          esac

      - name: Prepare binary
        run: |
          cp target/${{ matrix.target }}/release/lavalink-rust ${{ steps.artifact.outputs.binary }}
          chmod +x ${{ steps.artifact.outputs.binary }}

      - name: Upload binary artifact
        uses: actions/upload-artifact@v4
        with:
          name: ${{ steps.artifact.outputs.name }}
          path: ${{ steps.artifact.outputs.binary }}

  publish:
    name: Publish to crates.io
    runs-on: ubuntu-latest
    needs: [test, build]
    if: github.event_name == 'workflow_call' && github.ref == 'refs/heads/main'
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Install Rust toolchain
        uses: dtolnay/rust-toolchain@stable

      - name: Install system dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y \
            pkg-config \
            libssl-dev \
            libopus-dev \
            libavcodec-dev \
            libavformat-dev \
            libavutil-dev \
            libavfilter-dev \
            libavdevice-dev

      - name: Cache Rust dependencies
        uses: Swatinem/rust-cache@v2

      - name: Publish to crates.io
        env:
          CARGO_REGISTRY_TOKEN: ${{ secrets.CRATES_IO_TOKEN }}
        run: cargo publish --dry-run # Remove --dry-run when ready to publish

  build-docker:
    name: Build Docker Images
    needs: build
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        include:
          - name: Standard
            dockerfile: deployment/Dockerfile
            suffix: ''
            platforms: linux/amd64,linux/arm64
            artifact: lavalink-rust-linux-x64
          - name: Alpine
            dockerfile: deployment/Dockerfile.alpine
            suffix: '-alpine'
            platforms: linux/amd64,linux/arm64
            artifact: lavalink-rust-linux-x64-musl
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Download binary artifact
        uses: actions/download-artifact@v4
        with:
          name: ${{ matrix.artifact }}
          path: ./

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      # Skip registry login since we're only testing builds (push: false)

      - name: Docker Meta ${{ matrix.name }}
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: lavalink-rust-test
          flavor: |
            suffix=${{ matrix.suffix }},onlatest=true
          tags: |
            type=raw,value=test

      - name: Docker Build ${{ matrix.name }} and Push
        uses: docker/build-push-action@v6
        with:
          file: ${{ matrix.dockerfile }}
          context: .
          platforms: ${{ matrix.platforms }}
          push: false
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
